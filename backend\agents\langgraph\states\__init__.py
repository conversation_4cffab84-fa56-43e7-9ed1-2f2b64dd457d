"""
State definitions for LangGraph workflows in Datagenius.

This module contains state definitions for different types of workflows
and agent interactions in the Datagenius multi-agent system.
"""

from .agent_state import DatageniusAgentState
from .workflow_state import WorkflowState
from .collaboration_state import CollaborationState

__all__ = [
    "DatageniusAgentState",
    "WorkflowState", 
    "CollaborationState"
]
