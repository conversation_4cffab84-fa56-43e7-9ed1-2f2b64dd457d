"""
Legacy System Adapter for LangGraph Migration.

This module provides an adapter interface to the existing orchestrator
system, allowing the migration manager to execute workflows using the
legacy system when needed.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


class LegacyAdapter:
    """
    Adapter for interfacing with the legacy orchestrator system.
    
    This adapter provides:
    - Seamless integration with existing orchestrator
    - Request/response format handling
    - Error handling and fallback mechanisms
    - Performance monitoring for comparison
    """

    def __init__(self):
        """Initialize the legacy adapter."""
        self.logger = logging.getLogger(__name__)
        
        # Legacy system components (would be imported from actual modules)
        self.orchestrator = None  # Would be actual Orchestrator instance
        self.context_injector = None  # Would be actual UniversalContextInjector
        
        # Performance tracking
        self.execution_history: List[Dict[str, Any]] = []

    async def execute_workflow(
        self, 
        workflow_request: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Execute workflow using the legacy orchestrator system.
        
        Args:
            workflow_request: Workflow execution request
            context: Execution context
            
        Returns:
            Workflow execution result
        """
        try:
            start_time = datetime.now(timezone.utc)
            
            # Convert request to legacy format if needed
            legacy_request = self._prepare_legacy_request(workflow_request, context)
            
            # Execute using legacy orchestrator
            result = await self._execute_legacy_orchestrator(legacy_request)
            
            # Calculate execution metrics
            execution_time = (datetime.now(timezone.utc) - start_time).total_seconds()
            
            # Record execution for analysis
            self._record_execution(workflow_request, context, result, execution_time)
            
            # Format result for compatibility
            formatted_result = self._format_legacy_result(result, execution_time)
            
            self.logger.info(f"Legacy workflow executed successfully in {execution_time:.2f}s")
            return formatted_result
            
        except Exception as e:
            self.logger.error(f"Legacy workflow execution failed: {e}")
            
            # Record failed execution
            self._record_execution(workflow_request, context, {"error": str(e)}, 0.0, success=False)
            
            # Return error result
            return {
                "success": False,
                "error": str(e),
                "system": "legacy",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    def _prepare_legacy_request(
        self, 
        workflow_request: Dict[str, Any],
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare request in legacy orchestrator format."""
        try:
            # Extract message content
            message = workflow_request.get("message", {})
            if isinstance(message, dict):
                message_content = message.get("content", "")
            else:
                message_content = str(message)
            
            # Create legacy request format
            legacy_request = {
                "user_id": context.get("user_id", ""),
                "session_id": context.get("conversation_id", ""),
                "message": message_content,
                "business_profile_id": context.get("business_profile_id"),
                "context": context.get("business_context", {}),
                "attached_files": context.get("attached_files", []),
                "user_preferences": context.get("user_preferences", {}),
                "workflow_id": workflow_request.get("workflow_id"),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "metadata": {
                    "source": "langgraph_migration",
                    "original_request": workflow_request
                }
            }
            
            return legacy_request
            
        except Exception as e:
            self.logger.error(f"Error preparing legacy request: {e}")
            return {
                "user_id": context.get("user_id", "unknown"),
                "session_id": context.get("conversation_id", "unknown"),
                "message": str(workflow_request),
                "error": str(e)
            }

    async def _execute_legacy_orchestrator(self, legacy_request: Dict[str, Any]) -> Dict[str, Any]:
        """Execute request using the legacy orchestrator."""
        try:
            # This would interface with the actual legacy orchestrator
            # For now, we'll simulate the execution
            
            # Simulate legacy orchestrator behavior
            user_message = legacy_request.get("message", "")
            user_id = legacy_request.get("user_id", "")
            
            # Simulate agent selection (simplified)
            selected_agent = self._simulate_agent_selection(user_message)
            
            # Simulate agent execution
            agent_response = await self._simulate_agent_execution(selected_agent, user_message, legacy_request)
            
            # Create legacy result format
            result = {
                "success": True,
                "response": agent_response,
                "agent_id": selected_agent,
                "user_id": user_id,
                "session_id": legacy_request.get("session_id"),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "context": legacy_request.get("context", {}),
                "metadata": {
                    "execution_path": [selected_agent],
                    "tool_executions": 1,
                    "processing_time": 0.5
                }
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in legacy orchestrator execution: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    def _simulate_agent_selection(self, message: str) -> str:
        """Simulate legacy agent selection logic."""
        message_lower = message.lower()
        
        # Simple keyword-based routing (mimicking legacy behavior)
        if any(word in message_lower for word in ["analyze", "data", "chart", "graph"]):
            return "composable-analysis-ai"
        elif any(word in message_lower for word in ["marketing", "campaign", "content"]):
            return "composable-marketing-ai"
        elif any(word in message_lower for word in ["classify", "categorize", "label"]):
            return "classification-agent"
        else:
            return "datagenius-concierge"

    async def _simulate_agent_execution(
        self, 
        agent_id: str, 
        message: str, 
        context: Dict[str, Any]
    ) -> str:
        """Simulate agent execution and response generation."""
        # This would interface with actual agents
        # For now, return a simulated response
        
        agent_responses = {
            "datagenius-concierge": f"Hello! I'm the Datagenius Concierge. I can help you with: {message}",
            "composable-analysis-ai": f"I've analyzed your request: {message}. Here are the insights...",
            "composable-marketing-ai": f"For your marketing needs regarding: {message}, I recommend...",
            "classification-agent": f"I've classified your content: {message}. The category is..."
        }
        
        return agent_responses.get(agent_id, f"Response from {agent_id}: {message}")

    def _format_legacy_result(self, result: Dict[str, Any], execution_time: float) -> Dict[str, Any]:
        """Format legacy result for compatibility with migration system."""
        try:
            formatted_result = {
                "success": result.get("success", False),
                "system": "legacy",
                "execution_time": execution_time,
                "result": {
                    "content": result.get("response", ""),
                    "agent_id": result.get("agent_id", "unknown"),
                    "timestamp": result.get("timestamp", datetime.now(timezone.utc).isoformat()),
                    "metadata": result.get("metadata", {})
                },
                "quality_score": self._calculate_legacy_quality_score(result),
                "agent_transitions": len(result.get("metadata", {}).get("execution_path", [])),
                "tool_executions": result.get("metadata", {}).get("tool_executions", 0),
                "context": result.get("context", {}),
                "user_id": result.get("user_id"),
                "session_id": result.get("session_id")
            }
            
            # Add error information if present
            if "error" in result:
                formatted_result["error"] = result["error"]
                formatted_result["success"] = False
            
            return formatted_result
            
        except Exception as e:
            self.logger.error(f"Error formatting legacy result: {e}")
            return {
                "success": False,
                "system": "legacy",
                "execution_time": execution_time,
                "error": f"Result formatting error: {str(e)}"
            }

    def _calculate_legacy_quality_score(self, result: Dict[str, Any]) -> float:
        """Calculate quality score for legacy execution."""
        try:
            # Simple quality scoring based on result characteristics
            base_score = 0.7  # Base score for legacy system
            
            # Adjust based on success
            if result.get("success", False):
                base_score += 0.1
            else:
                base_score -= 0.3
            
            # Adjust based on response length (simple heuristic)
            response = result.get("response", "")
            if len(response) > 50:
                base_score += 0.1
            elif len(response) < 10:
                base_score -= 0.2
            
            # Adjust based on error presence
            if "error" in result:
                base_score -= 0.2
            
            return max(0.0, min(1.0, base_score))
            
        except Exception as e:
            self.logger.error(f"Error calculating legacy quality score: {e}")
            return 0.5  # Default score

    def _record_execution(
        self, 
        request: Dict[str, Any],
        context: Dict[str, Any],
        result: Dict[str, Any],
        execution_time: float,
        success: bool = True
    ):
        """Record execution for performance analysis."""
        try:
            execution_record = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "user_id": context.get("user_id"),
                "workflow_id": request.get("workflow_id"),
                "success": success,
                "execution_time": execution_time,
                "agent_id": result.get("agent_id", "unknown"),
                "quality_score": self._calculate_legacy_quality_score(result),
                "message_length": len(str(request.get("message", ""))),
                "has_attachments": bool(context.get("attached_files")),
                "business_profile_id": context.get("business_profile_id")
            }
            
            # Keep only recent executions (last 1000)
            self.execution_history.append(execution_record)
            if len(self.execution_history) > 1000:
                self.execution_history = self.execution_history[-1000:]
                
        except Exception as e:
            self.logger.error(f"Error recording execution: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for the legacy adapter."""
        try:
            if not self.execution_history:
                return {
                    "total_executions": 0,
                    "success_rate": 0.0,
                    "avg_execution_time": 0.0,
                    "avg_quality_score": 0.0
                }
            
            total_executions = len(self.execution_history)
            successful_executions = sum(1 for e in self.execution_history if e["success"])
            success_rate = successful_executions / total_executions
            
            execution_times = [e["execution_time"] for e in self.execution_history]
            avg_execution_time = sum(execution_times) / len(execution_times)
            
            quality_scores = [e["quality_score"] for e in self.execution_history if e["quality_score"] > 0]
            avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
            
            # Agent usage statistics
            agent_usage = {}
            for execution in self.execution_history:
                agent_id = execution.get("agent_id", "unknown")
                agent_usage[agent_id] = agent_usage.get(agent_id, 0) + 1
            
            return {
                "total_executions": total_executions,
                "success_rate": success_rate,
                "avg_execution_time": avg_execution_time,
                "avg_quality_score": avg_quality_score,
                "agent_usage": agent_usage,
                "recent_executions": self.execution_history[-10:],  # Last 10 executions
                "performance_trend": self._calculate_performance_trend()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return {"error": str(e)}

    def _calculate_performance_trend(self) -> Dict[str, Any]:
        """Calculate performance trend over recent executions."""
        try:
            if len(self.execution_history) < 20:
                return {"trend": "insufficient_data"}
            
            # Compare recent half vs older half
            recent_half = self.execution_history[len(self.execution_history)//2:]
            older_half = self.execution_history[:len(self.execution_history)//2]
            
            recent_avg_time = sum(e["execution_time"] for e in recent_half) / len(recent_half)
            older_avg_time = sum(e["execution_time"] for e in older_half) / len(older_half)
            
            recent_success_rate = sum(1 for e in recent_half if e["success"]) / len(recent_half)
            older_success_rate = sum(1 for e in older_half if e["success"]) / len(older_half)
            
            time_trend = "improving" if recent_avg_time < older_avg_time else "degrading"
            success_trend = "improving" if recent_success_rate > older_success_rate else "degrading"
            
            return {
                "execution_time_trend": time_trend,
                "success_rate_trend": success_trend,
                "recent_avg_time": recent_avg_time,
                "older_avg_time": older_avg_time,
                "recent_success_rate": recent_success_rate,
                "older_success_rate": older_success_rate
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating performance trend: {e}")
            return {"trend": "error", "error": str(e)}

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on legacy system components."""
        try:
            health_status = {
                "healthy": True,
                "components": {},
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Check orchestrator availability (simulated)
            health_status["components"]["orchestrator"] = {
                "status": "healthy",
                "response_time": 0.1
            }
            
            # Check context injector availability (simulated)
            health_status["components"]["context_injector"] = {
                "status": "healthy",
                "response_time": 0.05
            }
            
            # Check recent execution success rate
            recent_executions = self.execution_history[-10:] if self.execution_history else []
            if recent_executions:
                recent_success_rate = sum(1 for e in recent_executions if e["success"]) / len(recent_executions)
                if recent_success_rate < 0.8:
                    health_status["healthy"] = False
                    health_status["components"]["execution_success"] = {
                        "status": "degraded",
                        "success_rate": recent_success_rate
                    }
                else:
                    health_status["components"]["execution_success"] = {
                        "status": "healthy",
                        "success_rate": recent_success_rate
                    }
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"Error in legacy system health check: {e}")
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    def clear_execution_history(self):
        """Clear execution history (useful for testing or maintenance)."""
        self.execution_history.clear()
        self.logger.info("Legacy adapter execution history cleared")
