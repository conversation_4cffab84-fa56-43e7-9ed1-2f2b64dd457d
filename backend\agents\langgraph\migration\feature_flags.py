"""
Feature Flags System for LangGraph Migration.

This module provides a comprehensive feature flag system to enable
gradual rollout of LangGraph features while maintaining backward
compatibility with the existing orchestrator.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from enum import Enum

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from ...config import DATABASE_URL

logger = logging.getLogger(__name__)


class FeatureFlagStatus(str, Enum):
    """Feature flag status options."""
    DISABLED = "disabled"
    ENABLED = "enabled"
    TESTING = "testing"
    ROLLOUT = "rollout"
    DEPRECATED = "deprecated"


class FeatureFlagScope(str, Enum):
    """Feature flag scope options."""
    GLOBAL = "global"
    USER = "user"
    BUSINESS_PROFILE = "business_profile"
    AGENT = "agent"
    WORKFLOW = "workflow"


class FeatureFlags:
    """
    Comprehensive feature flag system for LangGraph migration.
    
    This system provides:
    - Granular feature control
    - A/B testing capabilities
    - Gradual rollout support
    - User-specific overrides
    - Performance monitoring
    """

    def __init__(self, database_url: str = None):
        """
        Initialize the feature flags system.
        
        Args:
            database_url: Database connection URL
        """
        self.database_url = database_url or DATABASE_URL
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # In-memory cache for performance
        self._flag_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamp = datetime.now(timezone.utc)
        self._cache_ttl = 300  # 5 minutes
        
        # Default feature flags for LangGraph migration
        self.default_flags = {
            "langgraph_enabled": {
                "status": FeatureFlagStatus.TESTING,
                "scope": FeatureFlagScope.GLOBAL,
                "description": "Enable LangGraph workflow system",
                "rollout_percentage": 0.0,
                "dependencies": []
            },
            "intelligent_routing": {
                "status": FeatureFlagStatus.DISABLED,
                "scope": FeatureFlagScope.GLOBAL,
                "description": "Enable intelligent agent routing with LangGraph",
                "rollout_percentage": 0.0,
                "dependencies": ["langgraph_enabled"]
            },
            "multi_agent_collaboration": {
                "status": FeatureFlagStatus.DISABLED,
                "scope": FeatureFlagScope.WORKFLOW,
                "description": "Enable multi-agent collaboration workflows",
                "rollout_percentage": 0.0,
                "dependencies": ["langgraph_enabled", "intelligent_routing"]
            },
            "state_persistence": {
                "status": FeatureFlagStatus.DISABLED,
                "scope": FeatureFlagScope.GLOBAL,
                "description": "Enable LangGraph state persistence and recovery",
                "rollout_percentage": 0.0,
                "dependencies": ["langgraph_enabled"]
            },
            "performance_monitoring": {
                "status": FeatureFlagStatus.ENABLED,
                "scope": FeatureFlagScope.GLOBAL,
                "description": "Enable enhanced performance monitoring",
                "rollout_percentage": 100.0,
                "dependencies": []
            },
            "legacy_fallback": {
                "status": FeatureFlagStatus.ENABLED,
                "scope": FeatureFlagScope.GLOBAL,
                "description": "Enable fallback to legacy orchestrator on errors",
                "rollout_percentage": 100.0,
                "dependencies": []
            }
        }
        
        self.logger = logging.getLogger(__name__)
        self._ensure_flags_table_exists()

    def _ensure_flags_table_exists(self):
        """Ensure the feature flags table exists in the database."""
        try:
            with self.engine.connect() as conn:
                # Create feature flags table if it doesn't exist
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS feature_flags (
                        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                        flag_name VARCHAR(255) NOT NULL UNIQUE,
                        status VARCHAR(50) NOT NULL DEFAULT 'disabled',
                        scope VARCHAR(50) NOT NULL DEFAULT 'global',
                        description TEXT,
                        rollout_percentage DECIMAL(5,2) DEFAULT 0.0,
                        dependencies TEXT[], -- Array of dependent flag names
                        configuration JSONB DEFAULT '{}',
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        created_by VARCHAR(255),
                        updated_by VARCHAR(255)
                    )
                """))
                
                # Create feature flag overrides table
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS feature_flag_overrides (
                        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                        flag_name VARCHAR(255) NOT NULL,
                        scope_type VARCHAR(50) NOT NULL, -- 'user', 'business_profile', 'agent', etc.
                        scope_value VARCHAR(255) NOT NULL, -- The actual ID
                        status VARCHAR(50) NOT NULL,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        created_by VARCHAR(255),
                        
                        CONSTRAINT fk_flag_override 
                            FOREIGN KEY (flag_name) 
                            REFERENCES feature_flags(flag_name) 
                            ON DELETE CASCADE,
                        
                        CONSTRAINT uk_flag_scope_override 
                            UNIQUE (flag_name, scope_type, scope_value)
                    )
                """))
                
                # Create indexes
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_feature_flags_name 
                        ON feature_flags(flag_name)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_flag_overrides_scope 
                        ON feature_flag_overrides(scope_type, scope_value)
                """))
                
                conn.commit()
                
                # Initialize default flags
                self._initialize_default_flags()
                
        except Exception as e:
            self.logger.error(f"Error ensuring feature flags table exists: {e}")

    def _initialize_default_flags(self):
        """Initialize default feature flags in the database."""
        try:
            session = self.SessionLocal()
            
            for flag_name, flag_config in self.default_flags.items():
                # Check if flag already exists
                existing_flag = session.execute(text("""
                    SELECT flag_name FROM feature_flags WHERE flag_name = :flag_name
                """), {"flag_name": flag_name}).fetchone()
                
                if not existing_flag:
                    # Insert new flag
                    session.execute(text("""
                        INSERT INTO feature_flags 
                        (flag_name, status, scope, description, rollout_percentage, dependencies)
                        VALUES (:flag_name, :status, :scope, :description, :rollout_percentage, :dependencies)
                    """), {
                        "flag_name": flag_name,
                        "status": flag_config["status"].value,
                        "scope": flag_config["scope"].value,
                        "description": flag_config["description"],
                        "rollout_percentage": flag_config["rollout_percentage"],
                        "dependencies": flag_config["dependencies"]
                    })
            
            session.commit()
            session.close()
            
        except Exception as e:
            self.logger.error(f"Error initializing default flags: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()

    async def is_enabled(
        self, 
        flag_name: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Check if a feature flag is enabled for the given context.
        
        Args:
            flag_name: Name of the feature flag
            context: Context information (user_id, business_profile_id, etc.)
            
        Returns:
            True if the feature is enabled, False otherwise
        """
        try:
            # Get flag configuration
            flag_config = await self._get_flag_config(flag_name)
            
            if not flag_config:
                self.logger.warning(f"Feature flag '{flag_name}' not found")
                return False
            
            # Check dependencies first
            if not await self._check_dependencies(flag_config.get("dependencies", []), context):
                return False
            
            # Check for specific overrides
            override_status = await self._check_overrides(flag_name, context)
            if override_status is not None:
                return override_status == FeatureFlagStatus.ENABLED
            
            # Check global status
            status = FeatureFlagStatus(flag_config.get("status", FeatureFlagStatus.DISABLED))
            
            if status == FeatureFlagStatus.DISABLED:
                return False
            elif status == FeatureFlagStatus.ENABLED:
                return True
            elif status in [FeatureFlagStatus.TESTING, FeatureFlagStatus.ROLLOUT]:
                # Check rollout percentage
                rollout_percentage = flag_config.get("rollout_percentage", 0.0)
                return await self._check_rollout_eligibility(flag_name, rollout_percentage, context)
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking feature flag '{flag_name}': {e}")
            return False

    async def _get_flag_config(self, flag_name: str) -> Optional[Dict[str, Any]]:
        """Get feature flag configuration from cache or database."""
        # Check cache first
        if self._is_cache_valid() and flag_name in self._flag_cache:
            return self._flag_cache[flag_name]
        
        # Refresh cache if needed
        if not self._is_cache_valid():
            await self._refresh_cache()
            
        return self._flag_cache.get(flag_name)

    async def _refresh_cache(self):
        """Refresh the feature flags cache from database."""
        try:
            session = self.SessionLocal()
            
            query = text("""
                SELECT flag_name, status, scope, description, rollout_percentage, 
                       dependencies, configuration
                FROM feature_flags
            """)
            
            results = session.execute(query).fetchall()
            session.close()
            
            # Update cache
            self._flag_cache = {}
            for result in results:
                self._flag_cache[result.flag_name] = {
                    "status": result.status,
                    "scope": result.scope,
                    "description": result.description,
                    "rollout_percentage": float(result.rollout_percentage) if result.rollout_percentage else 0.0,
                    "dependencies": result.dependencies or [],
                    "configuration": result.configuration or {}
                }
            
            self._cache_timestamp = datetime.now(timezone.utc)
            
        except Exception as e:
            self.logger.error(f"Error refreshing feature flags cache: {e}")
            if 'session' in locals():
                session.close()

    def _is_cache_valid(self) -> bool:
        """Check if the cache is still valid."""
        return (datetime.now(timezone.utc) - self._cache_timestamp).total_seconds() < self._cache_ttl

    async def _check_dependencies(
        self, 
        dependencies: List[str], 
        context: Optional[Dict[str, Any]]
    ) -> bool:
        """Check if all dependency flags are enabled."""
        for dependency in dependencies:
            if not await self.is_enabled(dependency, context):
                return False
        return True

    async def _check_overrides(
        self, 
        flag_name: str, 
        context: Optional[Dict[str, Any]]
    ) -> Optional[FeatureFlagStatus]:
        """Check for context-specific flag overrides."""
        if not context:
            return None
        
        try:
            session = self.SessionLocal()
            
            # Check for user-specific override
            if "user_id" in context:
                result = session.execute(text("""
                    SELECT status FROM feature_flag_overrides
                    WHERE flag_name = :flag_name 
                      AND scope_type = 'user' 
                      AND scope_value = :user_id
                """), {
                    "flag_name": flag_name,
                    "user_id": context["user_id"]
                }).fetchone()
                
                if result:
                    session.close()
                    return FeatureFlagStatus(result.status)
            
            # Check for business profile override
            if "business_profile_id" in context:
                result = session.execute(text("""
                    SELECT status FROM feature_flag_overrides
                    WHERE flag_name = :flag_name 
                      AND scope_type = 'business_profile' 
                      AND scope_value = :business_profile_id
                """), {
                    "flag_name": flag_name,
                    "business_profile_id": context["business_profile_id"]
                }).fetchone()
                
                if result:
                    session.close()
                    return FeatureFlagStatus(result.status)
            
            session.close()
            return None
            
        except Exception as e:
            self.logger.error(f"Error checking flag overrides: {e}")
            if 'session' in locals():
                session.close()
            return None

    async def _check_rollout_eligibility(
        self, 
        flag_name: str, 
        rollout_percentage: float, 
        context: Optional[Dict[str, Any]]
    ) -> bool:
        """Check if the context is eligible for rollout based on percentage."""
        if rollout_percentage >= 100.0:
            return True
        if rollout_percentage <= 0.0:
            return False
        
        # Use deterministic hash-based rollout
        # This ensures consistent behavior for the same context
        hash_input = f"{flag_name}:{context.get('user_id', 'anonymous')}"
        hash_value = hash(hash_input) % 100
        
        return hash_value < rollout_percentage

    async def set_flag(
        self, 
        flag_name: str, 
        status: FeatureFlagStatus,
        rollout_percentage: Optional[float] = None,
        updated_by: Optional[str] = None
    ) -> bool:
        """
        Set a feature flag status.
        
        Args:
            flag_name: Name of the feature flag
            status: New status for the flag
            rollout_percentage: Optional rollout percentage
            updated_by: User who updated the flag
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = self.SessionLocal()
            
            update_data = {
                "flag_name": flag_name,
                "status": status.value,
                "updated_by": updated_by
            }
            
            if rollout_percentage is not None:
                update_data["rollout_percentage"] = rollout_percentage
            
            # Update or insert flag
            session.execute(text("""
                INSERT INTO feature_flags (flag_name, status, rollout_percentage, updated_by)
                VALUES (:flag_name, :status, :rollout_percentage, :updated_by)
                ON CONFLICT (flag_name) 
                DO UPDATE SET 
                    status = EXCLUDED.status,
                    rollout_percentage = COALESCE(EXCLUDED.rollout_percentage, feature_flags.rollout_percentage),
                    updated_at = NOW(),
                    updated_by = EXCLUDED.updated_by
            """), update_data)
            
            session.commit()
            session.close()
            
            # Invalidate cache
            self._cache_timestamp = datetime.min.replace(tzinfo=timezone.utc)
            
            self.logger.info(f"Feature flag '{flag_name}' updated to {status.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting feature flag '{flag_name}': {e}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return False

    async def set_override(
        self, 
        flag_name: str, 
        scope_type: str, 
        scope_value: str,
        status: FeatureFlagStatus,
        created_by: Optional[str] = None
    ) -> bool:
        """
        Set a context-specific override for a feature flag.
        
        Args:
            flag_name: Name of the feature flag
            scope_type: Type of scope (user, business_profile, etc.)
            scope_value: Value for the scope (user ID, business profile ID, etc.)
            status: Override status
            created_by: User who created the override
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = self.SessionLocal()
            
            session.execute(text("""
                INSERT INTO feature_flag_overrides 
                (flag_name, scope_type, scope_value, status, created_by)
                VALUES (:flag_name, :scope_type, :scope_value, :status, :created_by)
                ON CONFLICT (flag_name, scope_type, scope_value)
                DO UPDATE SET 
                    status = EXCLUDED.status,
                    created_at = NOW(),
                    created_by = EXCLUDED.created_by
            """), {
                "flag_name": flag_name,
                "scope_type": scope_type,
                "scope_value": scope_value,
                "status": status.value,
                "created_by": created_by
            })
            
            session.commit()
            session.close()
            
            self.logger.info(f"Override set for flag '{flag_name}' on {scope_type}:{scope_value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting flag override: {e}")
            if 'session' in locals():
                session.rollback()
                session.close()
            return False

    async def get_all_flags(self) -> Dict[str, Dict[str, Any]]:
        """Get all feature flags and their current status."""
        await self._refresh_cache()
        return self._flag_cache.copy()

    async def get_flag_usage_stats(self, flag_name: str) -> Dict[str, Any]:
        """Get usage statistics for a feature flag."""
        # This would integrate with the monitoring system
        # For now, return basic information
        return {
            "flag_name": flag_name,
            "message": "Usage statistics would be collected from monitoring system"
        }
