"""
Utility nodes for LangGraph workflows.

This module provides utility node implementations for common
workflow operations like aggregation, transformation, and validation.
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import json

from ..states.agent_state import DatageniusAgentState, add_error

logger = logging.getLogger(__name__)


class UtilityNode:
    """
    Generic utility node for LangGraph workflows.
    
    This node performs utility operations on the workflow state.
    """

    def __init__(self, operation_name: str, operation_func: Callable[[DatageniusAgentState], DatageniusAgentState]):
        """
        Initialize the utility node.
        
        Args:
            operation_name: Name identifier for the operation
            operation_func: Function that performs the utility operation
        """
        self.operation_name = operation_name
        self.operation_func = operation_func
        self.logger = logging.getLogger(f"{__name__}.{operation_name}")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Execute utility operation and update state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state after utility operation
        """
        try:
            start_time = datetime.now()
            
            # Execute utility operation
            updated_state = self.operation_func(state)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Update execution metrics
            updated_state["execution_metrics"][f"{self.operation_name}_utility_time"] = execution_time

            self.logger.info(f"Utility operation {self.operation_name} completed in {execution_time:.2f}s")
            return updated_state

        except Exception as e:
            self.logger.error(f"Utility operation {self.operation_name} failed: {e}")
            return add_error(state, "utility_operation_error", str(e), {"operation_name": self.operation_name})


class AggregationNode:
    """
    Aggregation node for combining results from multiple sources.
    
    This node aggregates results from multiple agents or tools
    into a coherent response.
    """

    def __init__(self, aggregation_strategy: str = "synthesis", custom_aggregator: Optional[Callable] = None):
        """
        Initialize the aggregation node.
        
        Args:
            aggregation_strategy: Strategy for aggregation ("synthesis", "concatenation", "voting", "custom")
            custom_aggregator: Custom aggregation function if strategy is "custom"
        """
        self.aggregation_strategy = aggregation_strategy
        self.custom_aggregator = custom_aggregator
        self.logger = logging.getLogger(f"{__name__}.aggregation")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Aggregate results and update state.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with aggregated results
        """
        try:
            start_time = datetime.now()
            
            # Collect all agent outputs
            agent_outputs = state.get("agent_outputs", {})
            tool_results = state.get("tool_results", {})
            
            # Perform aggregation based on strategy
            if self.aggregation_strategy == "synthesis":
                aggregated_result = await self._synthesize_results(agent_outputs, tool_results)
            elif self.aggregation_strategy == "concatenation":
                aggregated_result = self._concatenate_results(agent_outputs, tool_results)
            elif self.aggregation_strategy == "voting":
                aggregated_result = self._vote_on_results(agent_outputs, tool_results)
            elif self.aggregation_strategy == "custom" and self.custom_aggregator:
                aggregated_result = self.custom_aggregator(agent_outputs, tool_results, state)
            else:
                raise ValueError(f"Unknown aggregation strategy: {self.aggregation_strategy}")
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Store aggregated result
            state["aggregated_result"] = {
                "strategy": self.aggregation_strategy,
                "result": aggregated_result,
                "timestamp": datetime.now().isoformat(),
                "execution_time": execution_time,
                "sources": {
                    "agents": list(agent_outputs.keys()),
                    "tools": list(tool_results.keys())
                }
            }
            
            # Add aggregated response message
            if isinstance(aggregated_result, dict) and "content" in aggregated_result:
                response_message = {
                    "role": "assistant",
                    "content": aggregated_result["content"],
                    "agent_id": "aggregator",
                    "timestamp": datetime.now().isoformat(),
                    "sources": list(agent_outputs.keys()) + list(tool_results.keys())
                }
                state["messages"].append(response_message)
            
            # Update execution metrics
            state["execution_metrics"]["aggregation_time"] = execution_time

            self.logger.info(f"Aggregation completed using {self.aggregation_strategy} strategy in {execution_time:.2f}s")
            return state

        except Exception as e:
            self.logger.error(f"Aggregation failed: {e}")
            return add_error(state, "aggregation_error", str(e))

    async def _synthesize_results(self, agent_outputs: Dict[str, Any], tool_results: Dict[str, Any]) -> Dict[str, Any]:
        """Synthesize results from multiple sources into a coherent response."""
        # Collect all content
        contents = []
        metadata = {
            "agents_involved": [],
            "tools_used": [],
            "synthesis_method": "intelligent_combination"
        }
        
        # Extract content from agent outputs
        for agent_id, output in agent_outputs.items():
            if isinstance(output, dict):
                result = output.get("result", {})
                if isinstance(result, dict) and "content" in result:
                    contents.append({
                        "source": agent_id,
                        "type": "agent",
                        "content": result["content"],
                        "quality": output.get("quality_score", 0.8)
                    })
                    metadata["agents_involved"].append(agent_id)
        
        # Extract content from tool results
        for tool_name, result in tool_results.items():
            if isinstance(result, dict) and "result" in result:
                tool_result = result["result"]
                if isinstance(tool_result, dict) and "content" in tool_result:
                    contents.append({
                        "source": tool_name,
                        "type": "tool",
                        "content": tool_result["content"],
                        "quality": 0.9  # Tools generally have consistent quality
                    })
                    metadata["tools_used"].append(tool_name)
        
        # Synthesize content
        if not contents:
            return {"content": "No results to synthesize.", "metadata": metadata}
        
        if len(contents) == 1:
            return {
                "content": contents[0]["content"],
                "metadata": metadata,
                "synthesis_note": "Single source, no synthesis needed"
            }
        
        # Multi-source synthesis
        synthesized_content = self._create_synthesized_content(contents)
        
        return {
            "content": synthesized_content,
            "metadata": metadata,
            "source_count": len(contents)
        }

    def _create_synthesized_content(self, contents: List[Dict[str, Any]]) -> str:
        """Create synthesized content from multiple sources."""
        # Sort by quality score
        contents.sort(key=lambda x: x.get("quality", 0), reverse=True)
        
        # Create structured synthesis
        synthesis = "Based on analysis from multiple sources:\n\n"
        
        for i, content_item in enumerate(contents, 1):
            source = content_item["source"]
            content = content_item["content"]
            source_type = content_item["type"]
            
            # Truncate long content for synthesis
            if len(content) > 300:
                content = content[:300] + "..."
            
            synthesis += f"**{i}. {source.title()} ({source_type}):**\n{content}\n\n"
        
        # Add synthesis conclusion
        synthesis += "**Summary:**\n"
        synthesis += "The above analysis provides multiple perspectives on your request. "
        synthesis += f"This synthesis combines insights from {len(contents)} different sources "
        synthesis += "to provide you with a comprehensive response."
        
        return synthesis

    def _concatenate_results(self, agent_outputs: Dict[str, Any], tool_results: Dict[str, Any]) -> Dict[str, Any]:
        """Simple concatenation of all results."""
        all_content = []
        sources = []
        
        # Concatenate agent outputs
        for agent_id, output in agent_outputs.items():
            if isinstance(output, dict):
                result = output.get("result", {})
                if isinstance(result, dict) and "content" in result:
                    all_content.append(f"**{agent_id}:** {result['content']}")
                    sources.append(agent_id)
        
        # Concatenate tool results
        for tool_name, result in tool_results.items():
            if isinstance(result, dict) and "result" in result:
                tool_result = result["result"]
                if isinstance(tool_result, dict) and "content" in tool_result:
                    all_content.append(f"**{tool_name}:** {tool_result['content']}")
                    sources.append(tool_name)
        
        return {
            "content": "\n\n".join(all_content),
            "aggregation_method": "concatenation",
            "sources": sources
        }

    def _vote_on_results(self, agent_outputs: Dict[str, Any], tool_results: Dict[str, Any]) -> Dict[str, Any]:
        """Vote-based aggregation for consensus building."""
        # This is a simplified voting mechanism
        # In practice, this would be more sophisticated
        
        votes = {}
        all_results = {}
        
        # Collect all results
        all_results.update(agent_outputs)
        all_results.update(tool_results)
        
        # Simple voting based on content similarity
        # This is a placeholder - real implementation would use semantic similarity
        
        if not all_results:
            return {"content": "No results to vote on.", "votes": {}}
        
        # For now, return the result with highest quality score
        best_result = None
        best_score = 0
        best_source = ""
        
        for source, result in all_results.items():
            if isinstance(result, dict):
                quality = result.get("quality_score", 0.5)
                if quality > best_score:
                    best_score = quality
                    best_result = result
                    best_source = source
        
        if best_result and isinstance(best_result, dict):
            result_content = best_result.get("result", {})
            if isinstance(result_content, dict):
                content = result_content.get("content", "")
            else:
                content = str(result_content)
        else:
            content = "No suitable result found through voting."
        
        return {
            "content": content,
            "aggregation_method": "voting",
            "winner": best_source,
            "winning_score": best_score,
            "total_participants": len(all_results)
        }


class ValidationNode:
    """
    Validation node for checking workflow state and results.
    
    This node validates the current state and results against
    predefined criteria.
    """

    def __init__(self, validation_rules: Dict[str, Callable], strict_mode: bool = False):
        """
        Initialize the validation node.
        
        Args:
            validation_rules: Dictionary of validation rule name to function mappings
            strict_mode: If True, any validation failure stops the workflow
        """
        self.validation_rules = validation_rules
        self.strict_mode = strict_mode
        self.logger = logging.getLogger(f"{__name__}.validation")

    async def __call__(self, state: DatageniusAgentState) -> DatageniusAgentState:
        """
        Validate state and update with validation results.
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated state with validation results
        """
        try:
            start_time = datetime.now()
            
            # Run all validation rules
            validation_results = {}
            all_passed = True
            
            for rule_name, rule_func in self.validation_rules.items():
                try:
                    result = rule_func(state)
                    validation_results[rule_name] = {
                        "passed": result,
                        "timestamp": datetime.now().isoformat()
                    }
                    if not result:
                        all_passed = False
                        self.logger.warning(f"Validation rule {rule_name} failed")
                except Exception as e:
                    validation_results[rule_name] = {
                        "passed": False,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
                    all_passed = False
                    self.logger.error(f"Validation rule {rule_name} error: {e}")
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Store validation results
            state["validation_results"] = {
                "all_passed": all_passed,
                "rules": validation_results,
                "strict_mode": self.strict_mode,
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }
            
            # Update execution metrics
            state["execution_metrics"]["validation_time"] = execution_time
            
            # Handle strict mode
            if self.strict_mode and not all_passed:
                failed_rules = [name for name, result in validation_results.items() if not result.get("passed", False)]
                error_msg = f"Validation failed in strict mode. Failed rules: {failed_rules}"
                return add_error(state, "validation_failure", error_msg)

            self.logger.info(f"Validation completed: {len(validation_results)} rules, all_passed={all_passed}")
            return state

        except Exception as e:
            self.logger.error(f"Validation process failed: {e}")
            return add_error(state, "validation_process_error", str(e))


# Factory functions for creating utility nodes

def create_utility_node(operation_name: str, operation_func: Callable) -> UtilityNode:
    """Create a generic utility node."""
    return UtilityNode(operation_name, operation_func)


def create_aggregation_node(**kwargs) -> AggregationNode:
    """Create an aggregation node."""
    return AggregationNode(**kwargs)


def create_validation_node(validation_rules: Dict[str, Callable], **kwargs) -> ValidationNode:
    """Create a validation node."""
    return ValidationNode(validation_rules, **kwargs)


# Common utility functions

def clean_state(state: DatageniusAgentState) -> DatageniusAgentState:
    """Clean up state by removing old or unnecessary data."""
    # Remove old messages beyond a threshold
    if len(state.get("messages", [])) > 50:
        state["messages"] = state["messages"][-25:]  # Keep last 25 messages
    
    # Clean up old tool results
    if len(state.get("tool_results", {})) > 20:
        # Keep only the most recent tool results
        recent_tools = list(state["tool_results"].keys())[-10:]
        state["tool_results"] = {
            k: v for k, v in state["tool_results"].items()
            if k in recent_tools
        }
    
    return state


def compress_large_data(state: DatageniusAgentState) -> DatageniusAgentState:
    """Compress large data structures in state."""
    # Compress large agent outputs
    for key, value in state.get("agent_outputs", {}).items():
        if isinstance(value, dict) and len(json.dumps(value)) > 10000:
            # Keep only essential information
            compressed = {
                "agent_id": value.get("agent_id"),
                "timestamp": value.get("timestamp"),
                "summary": str(value)[:500] + "... (compressed)",
                "compressed": True
            }
            state["agent_outputs"][key] = compressed
    
    return state


# Common validation rules

def has_required_outputs(state: DatageniusAgentState) -> bool:
    """Validate that required outputs are present."""
    return bool(state.get("agent_outputs") or state.get("tool_results"))


def no_critical_errors(state: DatageniusAgentState) -> bool:
    """Validate that there are no critical errors."""
    error_history = state.get("error_history", [])
    critical_error_types = ["system_error", "authentication_error", "permission_error"]
    
    return not any(error.get("type") in critical_error_types for error in error_history)


def quality_threshold_met(threshold: float = 0.7) -> Callable:
    """Create a validation rule for quality threshold."""
    def validate_quality(state: DatageniusAgentState) -> bool:
        quality_scores = state.get("quality_scores", {})
        if not quality_scores:
            return False
        
        avg_quality = sum(quality_scores.values()) / len(quality_scores)
        return avg_quality >= threshold
    
    return validate_quality
